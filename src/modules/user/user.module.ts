import bcrypt from 'bcrypt';
import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { jsonHookMongoosePlugin } from '../../mongoose-plugins';
import { setIdMongoosePlugin } from '../../mongoose-plugins';
import { User, UserDocument, UserSchema } from './user.schema';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { SharedModule } from '../../shared.module';
import { UserBroker } from './user.broker';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Referrals, ReferralsSchema } from './referrals/referrals.schema';
import {
  CatlogCredits,
  CatlogCreditsSchema,
  CatlogCreditsTransactions,
  CatlogCreditsTransactionsSchema,
} from './credits/credits.schema';
import { ReferralsService } from './referrals/referrals.service';
import { CatlogCreditsService } from './credits/credits.service';
import { ReferralsController } from './referrals/referrals.controller';
import { CreditsController } from './credits/credits.controller';
import { CatlogCreditsBroker } from './credits/credits.broker';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import { UserQueue } from './user.queue';
import { UsersJob } from './user-job.service';
import {
  FirebaseSubscription,
  FirebaseSubscriptionSchema,
  Notification,
  NotificationSchema,
  PushNotificationSubscription,
  PushNotificationSubscriptionSchema,
} from './notifications/notification.schema';
import { NotificationController } from './notifications/notification.controller';
import { NotificationService } from './notifications/notification.service';
import { FirebaseRepository } from '../../repositories/firebase.repository';
import { AdminConfigModule } from '../adminconfig/adminconfig.module';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QUEUES.USER,
    }),
    HttpModule,
    MongooseModule.forFeatureAsync([
      {
        name: User.name,
        useFactory: () => {
          const schema = UserSchema;
          schema.pre('save', function (this: UserDocument) {
            if (this.isModified('password')) {
              this.email = this.email.toLowerCase();
              //this.password =  bcrypt.hashSync(this.password, 10);
            }
          });
          schema.plugin(setIdMongoosePlugin());
          schema.plugin(
            jsonHookMongoosePlugin([
              '__v',
              '_id',
              'password',
              'email_verification_token',
              'phone_verification_token',
              'paystack_auth_code',
              'reset_password_token',
              'access_tokens',
            ]),
          );
          schema.plugin(mongoosePaginate);
          return schema;
        },
      },
      {
        name: Referrals.name,
        useFactory: () => {
          ReferralsSchema.plugin(setIdMongoosePlugin());
          ReferralsSchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          ReferralsSchema.plugin(mongoosePaginate);
          return ReferralsSchema;
        },
      },
      {
        name: CatlogCredits.name,
        useFactory: () => {
          CatlogCreditsSchema.plugin(setIdMongoosePlugin());
          CatlogCreditsSchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));

          return CatlogCreditsSchema;
        },
      },
      {
        name: CatlogCreditsTransactions.name,
        useFactory: () => {
          CatlogCreditsTransactionsSchema.plugin(setIdMongoosePlugin());
          CatlogCreditsTransactionsSchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          CatlogCreditsTransactionsSchema.plugin(mongoosePaginate);

          return CatlogCreditsTransactionsSchema;
        },
      },
      {
        name: Notification.name,
        useFactory: () => {
          const schema = NotificationSchema;
          schema.plugin(setIdMongoosePlugin());
          schema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          schema.plugin(mongoosePaginate);
          return schema;
        },
      },
      {
        name: PushNotificationSubscription.name,
        useFactory: () => {
          const schema = PushNotificationSubscriptionSchema;
          schema.plugin(setIdMongoosePlugin());
          schema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          return schema;
        },
      },
      {
        name: FirebaseSubscription.name,
        useFactory: () => {
          const schema = FirebaseSubscriptionSchema;
          schema.plugin(setIdMongoosePlugin());
          schema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          return schema;
        },
      },
    ]),
    SharedModule,
    AdminConfigModule,
  ],
  controllers: [
    UserBroker,
    UserController,
    ReferralsController,
    CreditsController,
    CatlogCreditsBroker,
    NotificationController,
  ],
  providers: [
    UserService,
    ReferralsService,
    CatlogCreditsService,
    UserQueue,
    UsersJob,
    NotificationService,
    FirebaseRepository,
  ],
  exports: [UserService, NotificationService],
})
export class UserModule {}
