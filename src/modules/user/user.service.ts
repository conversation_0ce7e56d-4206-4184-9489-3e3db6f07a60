import {
  BadRequestException,
  CACHE_MANAGER,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
  PreconditionFailedException,
  NotFoundException,
} from '@nestjs/common';
import Cache from 'cache-manager';
import * as mongoose from 'mongoose';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import * as bcrypt from 'bcrypt';
import { ONBOARDING_STEPS_WITH_REWARDS, User, USER_TYPE, UserDocument } from './user.schema';
import {
  FirebaseSubscription,
  FirebaseSubscriptionDocument,
  Notification,
  NotificationDocument,
  NOTIFICATION_TYPE,
  PushNotificationSubscription,
  PushNotificationSubscriptionDocument,
} from './notifications/notification.schema';
import {
  CreateUserDto,
  CreateUserDtoV2,
  FilterUserDto,
  FirebaseSubscriptionDto,
  LoginUserDto,
  NotificationInfoDto,
  PushNotificationSubscriptionDto,
  PushNotificationSubscriptionDtoWithCountry,
  RequestPasswordResetUserDto,
  ResetPasswordUserDto,
  SendVerificationTokenUserDto,
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserSubscriptionDto,
  VerifyTokenUserDto,
} from '../../models/dtos/UserDtos';
import { JwtService } from '@nestjs/jwt';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Subscription } from '../subscription/subscription.schema';
import { convertToObjectIds, formatPhoneNumber, genChars, generateNumber } from '../../utils';
import { MailchimpRepository, MailchimpUser } from '../../repositories/mailchimp.repository';
import { registerErrors } from '../../utils/errors.util';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES, Country } from '../country/country.schema';
import { Plan } from '../plan/plan.schema';
import { SUBSCRIPTION_STATUS } from '../../enums/payment.enum';
import { UserMessagingRepository } from '../../repositories/user-messaging.repository';
import { INTERNAL_ROLES, InternalDashboardPermissions, SCOPES, STORE_ROLES } from '../../utils/permissions.util';
import { Store } from '../store/store.schema';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { createHmac } from 'crypto';
import { ConfigService } from '@nestjs/config';
import { ApiGuardConfig } from '../../config/types/api-guard.config';
import { PushNotificationConfig } from '../../config/types/pushnotification.config';
import { setVapidDetails } from 'web-push';
import { sendNotification } from '../../utils/notifications.util';
import { Order } from '../orders/order.schema';
import dayjs from 'dayjs';
import { CatlogCreditsService } from './credits/credits.service';
import { ReferralsService } from './referrals/referrals.service';
import { CREDITS, getCountryFromPhone } from '../../utils/constants';
import { S3Repository } from '../../repositories/s3.repositories';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { Queue } from 'bull';
import { ReCaptchaRepository } from '../../repositories/recaptcha.repository';
import { BrevoRepository } from '../../repositories/brevo.repository';
import { GetExtendedContactDetails } from '@getbrevo/brevo';
import { PlanOption } from '../plan/plan-options/plan-options.schema';
import { createSubdomainURL, getDocId, hashForMeta, toUnixTimestamp } from '../../utils/functions';
import { CustomerIoRepository, CustomerIoUserObject } from '../../repositories/customer-io.repository';
import { Wallet, WalletAccountData } from '../wallets/wallet.schema';
import { Kyc } from '../store/kyc/kyc.schema';
import { Account } from '../wallets/wallet.account.schema';
import { get } from 'http';
import { ResendRepository } from '../../repositories/resend.repository';
import { CreateStoreDtoV2 } from '../../models/dtos/StoreDtos';
import { MetaConfig } from '../../config/types/meta.config';
import { removeNullUndefinedFromObject } from '../whatsapp-bot/utils/functions.utils';
import axios from 'axios';
import { PLAN_TYPE } from '../../enums/plan.enum';
import { FirebaseRepository } from '../../repositories/firebase.repository';
import { NotificationService } from './notifications/notification.service';
import { AdminConfigService } from '../adminconfig/adminconfig.service';
import { ADMIN_CONFIG } from '../adminconfig/adminconfig.schema';

@Injectable()
export class UserService {
  constructor(
    readonly logger: Logger,
    configService: ConfigService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    @InjectModel(User.name)
    readonly userModel: mongoose.PaginateModel<UserDocument>,
    @InjectModel(PushNotificationSubscription.name)
    private readonly pushNotificationModel: mongoose.Model<PushNotificationSubscriptionDocument>,
    @InjectModel(Notification.name)
    private readonly NotificationModel: Model<NotificationDocument>,
    @InjectModel(FirebaseSubscription.name)
    private readonly firebaseSubscriptionModel: Model<FirebaseSubscriptionDocument>,
    private readonly brokerTransport: BrokerTransportService,
    private readonly jwtService: JwtService,
    private readonly brevo: BrevoRepository,
    public readonly customerIo: CustomerIoRepository,
    public readonly firebase: FirebaseRepository,
    readonly whatsapp: UserMessagingRepository,
    private readonly config: ConfigService,
    private readonly creditsService: CatlogCreditsService,
    private readonly referralsService: ReferralsService,
    protected readonly s3: S3Repository,
    protected readonly recaptcha: ReCaptchaRepository,
    private readonly resend: ResendRepository,
    @InjectQueue(QUEUES.USER) private usersQueue: Queue,
    private readonly adminConfigService: AdminConfigService,
    private readonly notificationService?: NotificationService,
  ) {
    const cfg = config.get<PushNotificationConfig>('pushNotificationConfiguration');

    if (process.env.USE_NOTIFICATIONS === 'false') return;

    setVapidDetails('mailto:' + cfg.push_email, cfg.public_key, cfg.private_key);
  }

  async validateUserData(userRequest: CreateUserDto | CreateUserDtoV2) {
    const errorFields: any = {};

    if (await this.userModel.exists({ phone: userRequest.phone })) {
      errorFields.phone = 'Phone number has already been used';
    }

    if (await this.userModel.exists({ email: userRequest.email })) {
      errorFields.email = 'Email has already been used';
    }

    if (errorFields.email || errorFields.phone) {
      throw new HttpException(
        {
          statusCode: '400',
          message: 'Invalid fields',
          fields: errorFields,
          error: 'Bad request',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async createUserAndReferral(userRequest: CreateUserDto, isThirdParty: boolean = false) {
    let referrer: { exists: boolean; referral: string; user: { id: string; name: string } };
    const { referral_code, ...data } = userRequest;
    const userData = {
      ...data,
      password: bcrypt.hashSync(userRequest.password, 10),
      type: isThirdParty ? USER_TYPE.THIRD_PARTY : USER_TYPE.REGULAR,
    };

    if (referral_code) {
      referrer = await this.referralsService.isValidReferral(referral_code);

      if (referrer.exists) {
        (userData as any).referred_by = referrer.user.id; //add referred by if user was referred
      }
    }

    const user = await new this.userModel(userData).save();
    const referral = await this.referralsService.create(user?._id, user?.name);

    if (referral_code && referrer.user) {
      //update the referrer
      await this.referralsService.userReferred(referrer.referral, { user: user._id });
    }

    if (userData?.country) {
      await this.redeemReferralCredits(getDocId(user), userData?.country);
    }

    if (!isThirdParty) {
      const country = getCountryFromPhone(user?.phone);
      try {
        await this.brevo
          .createUser(user.email, user.name.split(' ')[0], user.name.split(' ')[1], country)
          .catch(registerErrors);
      } catch (error) {
        this.logger.error(error);
      }

      await this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user.email,
        first_name: user.name.split(' ')[0],
        last_name: user.name.split(' ')[1],
        phone: user.phone.split('-').join(''),
        country: country,
        created_at: Math.floor(Date.now() / 1000), //current unix timestamp
        referred_by: referrer?.user?.name,
      });

      await this.customerIo.trackUserEvent({
        userId: getDocId(user).toString(),
        name: 'signup',
        data: {
          email: user.email,
          name: user.name,
          phone: user.phone.split('-').join(''),
        },
      });
    }

    // Temporarily paused welcome email sending due to external integration
    // await this.usersQueue.add(
    //   QUEUES.USER,
    //   {
    //     type: JOBS.SEND_WELCOME_EMAILS,
    //     data: {
    //       user_name: user.name,
    //       email: user.email,
    //     },
    //   },
    //   {
    //     delay: 60 * 1000, //1 minute
    //   },
    // );

    return { user, referral };
  }

  async create(userRequest: CreateUserDto, isThirdParty: boolean = false) {
    await this.validateUserData(userRequest);
    const { user, referral } = await this.createUserAndReferral(userRequest, isThirdParty);

    return { user, referral };
  }

  async createUserV2(userRequest: CreateUserDtoV2, isThirdParty: boolean = false) {
    await this.validateUserData(userRequest);
    const { business_name, ...reqData } = userRequest;
    const { user, referral } = await this.createUserAndReferral(reqData, isThirdParty);

    const userWithStore = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.STORE.CREATE_STORE_V2, {
        user,
        store: {
          name: business_name,
          country: userRequest.country,
          store_type: userRequest.store_type,
          phone: userRequest.phone,
        } as CreateStoreDtoV2,
      })
      .toPromise();

    return { user: userWithStore, referral };
  }

  getUserRole(user: User, store_id: string) {
    const store = user.stores.find((store) => store.id === store_id);

    if (store) {
      const isStoreOwner = String(store.owner) === user.id;
      if (isStoreOwner) return STORE_ROLES.OWNER;

      const role = store.owners.find((owner) => owner.user === user.id)?.role;
      if (role) return STORE_ROLES[role];
    }

    return undefined;
  }
  checkStoreOwnerDisabled(store: Store, userId: string) {
    const ownerData = store.owners.find((data) => data.user === userId);
    return ownerData === undefined ? false : ownerData.disabled;
  }

  filterValidStores(user: User) {
    let userCopy = { ...user }; //change to const later
    const hasStores = userCopy.stores.length > 0;
    for (let i = userCopy.stores.length - 1; i >= 0; i--) {
      const store = userCopy.stores[i];
      if (store.disabled || this.checkStoreOwnerDisabled(store, userCopy.id)) {
        userCopy.stores.splice(i, 1);
      }
    }
    return {
      stores: [...userCopy.stores],
      storesDisabled: hasStores && userCopy.stores.length === 0,
    };
  }

  async login(incomingReq: LoginUserDto, order_id?: string, req?: any) {
    let user: UserDocument;

    if (incomingReq.email.startsWith('xadmin+')) {
      // Admin is attempting to log in as a user

      // Extract the target user's email
      const userEmail = incomingReq.email.substring('xadmin+'.length).trim();

      // Validate admin password using apiGuardConfig
      const apiGuardCfg = this.config.get<ApiGuardConfig>('apiGuardConfig');

      // If the admin password is incorrect, throw an error
      const correctAdminPassword = incomingReq.password === apiGuardCfg.password;

      if (!correctAdminPassword) {
        throw new UnauthorizedException('Admin password is incorrect');
      }

      // Fetch the target user
      user = await this.userModel.findOne({ email: userEmail }).populate({
        path: 'stores',
        select: '-security_pin',
        populate: {
          path: 'subscription',
          populate: {
            path: 'plan plan_option last_payment',
          },
        },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }
    } else {
      // Regular user login
      user = await this.userModel.findOne({ email: incomingReq.email }).populate({
        path: 'stores',
        select: '-security_pin',
        populate: {
          path: 'subscription',
          populate: {
            path: 'plan plan_option last_payment',
          },
        },
      });

      if (!user) {
        throw new UnauthorizedException('Email or password is incorrect');
      }

      const correctPassword = await bcrypt.compare(incomingReq.password, user.password);

      if (!correctPassword) {
        throw new UnauthorizedException('Email or password is incorrect');
      }
    }

    user = await user.toJSON();
    /* const { stores, storesDisabled } = this.filterValidStores(user);
    user.stores = stores;
    user.storesDisabled = storesDisabled;
    */

    // Handle mobile app signin counter tracking
    if (req?.headers?.is_mobile_app === 'true') {
      const hasPosition = await this.userHasMobileAppSigninPosition(user.id);
      if (!hasPosition) {
        const position = await this.incrementMobileAppSigninCounter();
        await this.assignMobileAppSigninPosition(user.id, position);
        // Update the user object with the new position
        if (!user.meta) user.meta = {};
        user.meta.mobile_app_signin_position = position;
        user.is_first_mobile_app_signin = true;
      }
    }

    let store_id = user.stores[0]?.id;

    if (order_id) {
      const order = await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN, { _id: order_id })
        .toPromise();

      const order_store = user.stores.find((s) => s._id == (order.store as Store)._id);

      if (!order_store) {
        throw new UnauthorizedException("Login details don't match this order");
      }

      store_id = order_store.id;
    }

    // Fetch domains for each store using brokerTransport
    const storeIds = user.stores.map((s) => s._id);
    const storesWithDomains = await Promise.all(
      storeIds.map(async (storeId) => {
        // Use the broker to fetch the store with domains populated
        const store = await this.brokerTransport
          .send<any>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
          .toPromise();
        return { _id: storeId, domains: store?.domains || [] };
      }),
    );
    const domainsMap = new Map(storesWithDomains.map((s) => [s._id.toString(), s.domains]));
    user.stores = user.stores.map((store) =>
      Object.assign({}, store, { domains: domainsMap.get(store._id.toString()) || [] }),
    );

    const token = await this.generateJwtToken(user, store_id);
    this.userModel.findByIdAndUpdate(user.id, { last_login: new Date() }).exec();

    user.subscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, {
        owner: user.id,
      })
      .toPromise();

    const storePromises = user.stores.map(async (store) => {
      // store.item_count = await this.brokerTransport
      //   .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, { store: store.id })
      //   .toPromise();

      store.country = await this.brokerTransport
        .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
          code: store.country,
        })
        .toPromise();

      const subscription = store.subscription as Subscription;

      store.subscription = subscription
        ? {
            ...subscription,
            plan: {
              plan_option_id: (subscription.plan_option as any)._id,
              ...(subscription.plan_option as PlanOption),
              ...(subscription.plan as Plan),
            } as any,
          }
        : null;

      return store;
    });

    if (user?.meta?.added_to_customer_io) {
      this.customerIo.trackUserEvent({
        userId: getDocId(user).toString(),
        name: 'login',
        data: {
          email: user.email,
          name: user.name,
          phone: user.phone.split('-').join(''),
        },
      });
    } else {
      //create user on customer.io
      this.createUserOnCustomerIo(user.id, true);
    }

    user.stores = await Promise.all(storePromises);
    return {
      user,
      token: token,
    };
  }

  async setupVideoWatched(userId: string) {
    let user = await this.userModel.findById(userId);

    if (user.meta?.has_watched_onboarding_video || user?.onboarding_rewards?.setup_video_credits_earned)
      throw new BadRequestException('User has already earned credits');

    const response = await this.creditsService.addCreditsForOnboardingSteps(
      ONBOARDING_STEPS_WITH_REWARDS.WATCH_SETUP_VIDEO,
      getDocId(user),
    );

    if (response?.error) throw new BadRequestException(response?.error);

    user = await this.userModel.findByIdAndUpdate(
      getDocId(user),
      { 'meta.has_watched_onboarding_video': true },
      { new: true },
    );

    return user;
  }

  async createUserOnCustomerIo(user: User, fireLoginEvent = false) {
    // const user = await this.userModel.findById(userId);
    let referrer = null;
    let accounts = null;

    if (!(user as any)?.createdAt) return;

    if (user?.referred_by) {
      referrer = await this.userModel.findById(user?.referred_by);
    }

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: user?.primary_store })
      .toPromise();

    const productsCount = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, {
        store: getDocId(store),
      })
      .toPromise();

    const paymentCount = await this.brokerTransport
      .send<{ count: number; total_amount: number }>(
        BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS,
        getDocId(user).toString(),
      )
      .toPromise();

    const kyc = store?.kyc
      ? await this.brokerTransport
          .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { _id: store?.kyc })
          .toPromise()
      : null;

    if (kyc && kyc.status === 'APPROVED' && kyc.country === COUNTRY_CODE.NG && store?.wallet) {
      accounts = await this.brokerTransport
        .send<Account[]>(BROKER_PATTERNS.WALLET.GET_ACCOUNTS, store?.wallet)
        .toPromise();
    }

    let customerIoData: CustomerIoUserObject = {
      email: user.email,
      id: getDocId(user).toString(),
      first_name: user.name.split(' ')[0],
      last_name: user.name.split(' ')[1],
      phone: user.phone.split('-').join(''),
      country: getCountryFromPhone(user.phone),
      created_at: toUnixTimestamp((user as any)?.createdAt),
      referred_by: referrer?.name,
      store_name: store?.name,
      store_link: store?.slug ? createSubdomainURL(process.env.CATLOG_WWW, store?.slug) : '',
      store_country: store?.country as string,
      store_logo: store?.logo,
      plan: store?.current_plan?.plan_type,
      plan_interval: store?.current_plan?.interval_text,
      business_category: store?.business_category?.name,
      products_count: productsCount,
      no_of_subscription_payments_made: paymentCount.count,
      kyc_status: kyc ? kyc?.status : null,
      bank_account_created: accounts?.length > 0,
      store_type: store?.flags?.uses_chowbot ? 'chowbot' : 'regular',
    };

    await this.customerIo.createOrUpdateUser(customerIoData);

    if (fireLoginEvent) {
      this.customerIo.trackUserEvent({
        userId: getDocId(user).toString(),
        name: 'login',
        data: {
          email: user.email,
          name: user.name,
          phone: user.phone.split('-').join(''),
        },
      });
    }
  }

  async internalLogin(username: string, password: string) {
    const apiGuardCfg = this.config.get<ApiGuardConfig>('apiGuardConfig');

    if (apiGuardCfg.username != username || apiGuardCfg.password != password) {
      // Check if this is a user with internal dashboard access
      const user = await this.userModel.findOne({ email: username });

      if (!user || !user.internal_dashboard_role) {
        throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
      }

      // Verify password for user
      const correctPassword = await bcrypt.compare(password, user.password);
      if (!correctPassword) {
        throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
      }

      // Get permissions for the user's role
      const permissions = InternalDashboardPermissions[user.internal_dashboard_role] || [];

      // User is authenticated and has internal dashboard role
      return {
        token: this.jwtService.sign({
          userId: user.id,
          type: 'internal',
          role: user.internal_dashboard_role,
          internalRole: user.internal_dashboard_role,
          permissions: permissions,
        }),
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.internal_dashboard_role,
        },
      };
    }

    // Legacy internal API key auth - this is the admin user from config
    // Treat as SUPER_ADMIN with all possible permissions
    const id = createHmac('sha256', username + ':' + password)
      .update(apiGuardCfg.rn)
      .digest('hex');

    // Include admin role and all permissions in the token
    const allInternalPermissions = Object.values(SCOPES.INTERNAL_DASHBOARD);

    return {
      token: this.jwtService.sign({
        id: id,
        type: 'internal',
        internalRole: INTERNAL_ROLES.SUPER_ADMIN,
        permissions: allInternalPermissions,
      }),
      user: {
        id: id,
        role: INTERNAL_ROLES.SUPER_ADMIN,
      },
    };
  }

  async userCount() {
    return await this.userModel.find({}).countDocuments();
  }

  async updateLastLogin(id: string) {
    await this.userModel.findByIdAndUpdate(id, { last_login: new Date() });
  }

  async profileDetails(id) {
    return this.userModel.findById(id);
  }

  /**
   * Generates a new user jwt token
   * @param user
   */
  async generateJwtToken(user: User, storeId?: string) {
    let index = user.stores.findIndex((s) => s.id === storeId);
    index = index < 0 ? 0 : index;

    const store = user.stores[index];
    const sub = store?.subscription as Subscription;

    return this.jwtService.sign({
      id: user.id,
      type: 'new',
      store: { id: store?.id, name: store?.name, subscription: sub?._id },
    });
  }

  /**
   * Gets and returns one user
   * @param filter
   * @param multiple
   */
  async getUser(filter: mongoose.FilterQuery<UserDocument>, multiple = false, select = '-password') {
    if (multiple) {
      return this.userModel.find(filter, select);
    }

    let user = (await this.userModel.findOne(filter).populate('stores')) as UserDocument;

    if (user) {
      try {
        user = user?.toJSON();

        user.subscription = await this.brokerTransport
          .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, {
            owner: user.id,
          })
          .toPromise();
      } catch (error) {
        console.log(error);
      }
    }

    return user;
  }

  /*
    Sets a new debtor subscription deadline
  */
  async updateDebtorSubscriptionsDeadline() {
    let subscriptions = await this.brokerTransport
      .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_MULTIPLE_SUBSCRIPTIONS, {
        filter: {
          next_payment_date: {
            $lt: new Date(),
          },
        },
      })
      .toPromise();
    const res = [];
    subscriptions = subscriptions.filter((s) => (s.plan as Plan | undefined)?.type !== 'STARTER' && s.plan);
    for (const subscription of subscriptions) {
      subscription.next_payment_date = new Date(Date.now() + 1000 * 60 * 60 * 24 * 2);

      const sub = await this.brokerTransport
        .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
          filter: { _id: subscription.id },
          update: {
            next_payment_date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2),
          },
        })
        .toPromise();

      res.push(sub);
    }
    return res;
  }

  /**
   * Gets and returns all users that have not paid in a while
   */
  async getDebtors() {
    const monthDifference = (d1: Date, d2: Date) => {
      let months: number;
      months = (d2.getFullYear() - d1.getFullYear()) * 12;
      months -= d1.getMonth();
      months += d2.getMonth();
      return months <= 0 ? 0 : months;
    };

    let subscriptions = await this.brokerTransport
      .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_MULTIPLE_SUBSCRIPTIONS, {
        filter: {
          next_payment_date: {
            $lt: new Date(),
          },
        },
      })
      .toPromise();

    const users = await Promise.all(
      subscriptions.map(async (s) => {
        const u = (await this.getUser({ _id: s.owner })) as UserDocument;
        u.subscription = s;

        return u;
      }),
    );

    const owingUsersSummary = users
      .filter((u) => {
        const plan = u?.subscription?.plan as Plan;

        return plan && plan.type !== 'STARTER';
      })
      .map((u) => {
        const { subscription } = u;
        const paymentDate = new Date(subscription.next_payment_date);

        subscription.next_payment_date = new Date(Date.now() + 1000 * 60 * 60 * 24 * 2);

        this.brokerTransport
          .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
            filter: { owner: u._id },
            update: { next_payment_date: subscription.next_payment_date },
          })
          .toPromise()
          .then(console.log);

        return {
          name: u.name,
          phone: u.phone,
          store_name: u.stores[0].name,
          store_link: `https://catlog.shop/${u.stores[0].slug}`,
          months_owed: monthDifference(paymentDate, new Date()),
        };
      });

    return owingUsersSummary;
  }

  /**
   * Gets and messages all users that have not paid in a while
   */
  async messageDebtors() {
    const discounts = {
      1: {
        discount: '10%',
        payment_link: 'https://paystack.com/pay/basic-plan-1-month-discount',
      },
      2: {
        discount: '15%',
        payment_link: 'https://paystack.com/pay/basic-plan-2-months-discount',
      },
      3: {
        discount: '20%',
        payment_link: 'https://paystack.com/pay/basic-plan-3-months-discount',
      },
      4: {
        discount: '20%',
        payment_link: 'https://paystack.com/pay/basic-plan-4-months-discount',
      },
      5: {
        discount: '20%',
        payment_link: 'https://paystack.com/pay/basic-plan-5-months-discount',
      },
    };

    const monthDifference = (d1: Date, d2: Date) => {
      let months: number;
      months = (d2.getFullYear() - d1.getFullYear()) * 12;
      months -= d1.getMonth();
      months += d2.getMonth();
      return months <= 0 ? 0 : months;
    };

    let subscriptions = await this.brokerTransport
      .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_MULTIPLE_SUBSCRIPTIONS, {
        filter: {
          next_payment_date: {
            $lt: new Date(),
          },
        },
      })
      .toPromise();

    const users = await Promise.all(
      subscriptions.map(async (s) => {
        const u = (await this.getUser({ _id: s.owner })) as UserDocument;
        u.subscription = s;
        return u;
      }),
    );

    const owingUsersSummary = users.map((u) => {
      const { subscription } = u;
      const paymentDate = new Date(subscription.next_payment_date);

      return {
        name: u.name,
        phone: u.phone,
        months_owed: monthDifference(paymentDate, new Date()),
      };
    });

    for (const owingUser of owingUsersSummary) {
      // if (owingUser.months_owed > 1) continue; // Don't remove until approved
      if (['+234-8156588350', '+234-8050483098'].indexOf(owingUser.phone) > -1) continue; // Skip users already messaged

      owingUser.months_owed = owingUser.months_owed == 0 ? 1 : owingUser.months_owed;

      console.log('executed for', owingUser, formatPhoneNumber(owingUser.phone), discounts[owingUser.months_owed]);

      // const response = await this.brokerTransport
      //   .emit(BROKER_PATTERNS.SMS.SEND_WHATSAPP, {
      //     to: formatPhoneNumber(owingUser.phone),
      //     channel: 'whatsapp',
      //     data: {
      //       sms: getScript(owingUser, discounts),
      //     },
      //   })
      //   .toPromise();
    }

    return owingUsersSummary;
  }

  async getUserProfile(filter: mongoose.FilterQuery<UserDocument>) {
    let user = await this.userModel.findOne(filter).populate({
      path: 'stores',
      select: '-security_pin',
      populate: [
        { path: 'pickup_address' },
        {
          path: 'subscription',
          populate: {
            path: 'plan plan_option last_payment',
          },
        },
        {
          path: 'domains',
          match: { verified: true, certificate_issued: true },
        },
      ],
    });
    user = await user.toJSON();

    /* const { stores, storesDisabled } = this.filterValidStores(user);
    user.stores = stores;
    user.storesDisabled = storesDisabled; */

    user.subscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, {
        owner: user.id,
      })
      .toPromise();

    const storePromises = user.stores.map(async (store) => {
      // store.item_count = await this.brokerTransport
      //   .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, { store: store.id })
      //   .toPromise();

      store.country = await this.brokerTransport
        .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
          code: store.country,
        })
        .toPromise();

      const subscription = store.subscription as Subscription;

      store.subscription = store?.subscription
        ? {
            ...subscription,
            plan: {
              plan_option_id: (subscription.plan_option as any)._id,
              ...(subscription.plan_option as PlanOption),
              ...(subscription.plan as Plan),
            } as any,
          }
        : null;

      delete store.access_tokens;

      return store;
    });

    user.stores = await Promise.all(storePromises);
    return user;
  }

  async update(id: string, req: UpdateUserDto) {
    const user = await this.userModel.findById(id);

    if (!user) {
      throw new BadRequestException('user with id does not exists which means jwt token is invalid');
    }

    user.auto_debit_wallets = req.auto_debit_wallets ?? user.auto_debit_wallets ?? true;

    const samePhone = await this.userModel.findOne({ phone: req.phone });

    if (req?.phone && req.phone !== user?.phone && samePhone) {
      throw new BadRequestException('This phone number is already in use');
    }

    const sameEmail = await this.userModel.findOne({ email: req.email });

    if (req?.email && req.email !== user?.email && sameEmail) {
      throw new BadRequestException('This email is already in use');
    }

    if (req?.phone && req.phone !== user?.phone) user.phone_verified = false;
    if (req?.email && req.email !== user?.email) user.email_verified = false;

    if (req.name) user.name = req.name;
    if (req.onboarding_steps) user.onboarding_steps = req.onboarding_steps;

    try {
      const existsOnBrevo = await this.brevo.userExists(user.email);

      if (req.email && req.email !== user?.email) {
        if (existsOnBrevo) await this.brevo.updateEmail(user.email, req.email);
        await this.customerIo.createOrUpdateUser({ id: getDocId(user), email: req.email });
        user.email = req.email;
      }

      //UPDATE MAILCHIMP USER'S COUNTRY IF PHONE NUMBER CHANGES & COUNTRY CODE CHANGES
      if (req.phone && req.phone !== user?.phone) {
        // const country = req?.phone.startsWith('+233') ? COUNTRY_CODE.GH : COUNTRY_CODE.NG;

        const country = getCountryFromPhone(req?.phone);

        const countryIsSame = req?.phone.startsWith(user?.phone.substring(0, 4));
        if (existsOnBrevo && !countryIsSame) {
          await this.brevo.updateUserCountry(req.email, country);
        }
        if (!countryIsSame) {
          await this.customerIo.createOrUpdateUser({
            id: getDocId(user),
            email: user?.email,
            phone: req.phone.split('-').join(''),
            country: country,
          });
        }
        user.phone = req.phone;
      }

      if (!existsOnBrevo) {
        // const country = user?.phone.startsWith('+233') ? COUNTRY_CODE.GH : COUNTRY_CODE.NG;
        const country = getCountryFromPhone(user?.phone);
        await this.brevo.createUser(user.email, user.name.split(' ')[0], user.name.split(' ')[1], country);
      }
    } catch (err) {
      if (req.email && req.email !== user?.email) user.email = req.email; // Update email in DB even if mailchimp fails
      if (req.phone && req.phone !== user?.phone) user.phone = req.phone; // Update phone in DB even if mailchimp fails

      console.log('MAILCHIMPCATCH', err, err?.response, err?.response?.data);
    }

    await this.userModel.findByIdAndUpdate(user.id, user);
    return this.getUserProfile({ _id: user.id });
  }

  async brokerUpdateUser(filter: mongoose.FilterQuery<UserDocument>, update: UserDocument) {
    return this.userModel.findOneAndUpdate(filter, update, { new: true });
  }

  async updatePassword(id: string, reqBody: UpdateUserPasswordDto) {
    const user = await this.getAndValidateUser(id);

    const correctPassword = await bcrypt.compare(reqBody.current_password, user.password);

    if (!correctPassword) {
      throw new BadRequestException('Old password is incorrect');
    }

    return this.userModel.findByIdAndUpdate(
      user.id,
      {
        password: bcrypt.hashSync(reqBody.new_password, 10),
      },
      { new: true },
    );
  }

  async sendVerificationTokens(id: string, body: SendVerificationTokenUserDto) {
    const user = await this.getAndValidateUser(id);
    if (body.email) {
      if (user.email_verified) {
        throw new BadRequestException('Email has already been verified');
      }
      user.email_verification_token = generateNumber(6);
      await user.save();

      this.resend.sendEmail(BROKER_PATTERNS.MAIL.VERIFY_EMAIL, {
        to: user.email,
        subject: "Let's verify your email 📬",
        data: {
          name: user.name.split(' ')[0],
          code: user.email_verification_token,
        },
      });
    }

    if (body.phone) {
      user.phone_verification_token = generateNumber(6);
      await user.save();

      if (user.phone_verified) {
        throw new BadRequestException('Phone has already been verified');
      }

      this.whatsapp.sendVerificationTokenMessage(formatPhoneNumber(user.phone), user.phone_verification_token);

      // await this.brokerTransport.emit(BROKER_PATTERNS.SMS.SEND_WHATSAPP, {
      //   to: formatPhoneNumber(user.phone),
      //   channel: 'whatsapp',
      //   data: {
      //     sms: `Your Catlog verification code is ${user.phone_verification_token}.`,
      //   },
      // });
    }
  }

  /**
   * Refreshes the jwt token
   * @param userId
   */
  async refreshJwtToken(userId: string, store?: string) {
    let user = await this.getAndValidateUser(userId);
    const token = await this.generateJwtToken(user, store);
    return { token };
  }

  async verifyJwtToken(token: string) {
    try {
      return await this.jwtService.verifyAsync(token);
    } catch (err) {
      this.logger.error('error verifying token');
      this.logger.error(err);
      return null;
    }
  }

  async verifyVerificationTokens(id, req: VerifyTokenUserDto) {
    const user = await this.getAndValidateUser(id);

    let emailVerified = false;
    let phoneVerified = false;

    if (req.email) {
      if (user.email_verification_token && user.email_verification_token === req.email) {
        user.email_verified = true;
        user.email_verification_token = undefined;
        emailVerified = true;
      } else {
        throw new BadRequestException('Invalid email verification token');
      }
    }

    if (req.phone) {
      if (user.phone_verification_token && user.phone_verification_token === req.phone) {
        user.phone_verified = true;
        user.phone_verification_token = undefined;
        phoneVerified = true;
      } else {
        throw new BadRequestException('Invalid phone verification token');
      }
    }

    if (!emailVerified && !phoneVerified) {
      throw new BadRequestException('No valid verification token provided');
    }

    const savedUser = (await user.save()).toJSON();
    savedUser.stores = savedUser.stores.map((store) => store.id);

    return {
      user: savedUser,
      message: 'Token verification successful',
    };
  }

  async getAndValidateUser(id: string) {
    const user = await this.userModel.findById(id).populate({
      path: 'stores',
      populate: [
        {
          path: 'subscription',
          populate: {
            path: 'plan last_payment',
          },
        },
        {
          path: 'domains',
          match: { verified: true, certificate_issued: true },
        },
      ],
    });
    if (!user) {
      throw new BadRequestException('user with id does not exists');
    }
    return user;
  }

  async updateCustomerIoStatus(id: string, status: boolean) {
    const user = await this.userModel.findByIdAndUpdate(id, { 'meta.added_to_customer_io': status });
    return user;
  }

  async getReferrals(id: string) {
    return await this.referralsService.getReferrals(id);
  }

  checkUser(user: UserDocument | null): UserDocument {
    if (!user) {
      throw new BadRequestException('user with id does not exist');
    }

    return user;
  }

  async requestPasswordReset(reqBody: RequestPasswordResetUserDto) {
    const user = await this.userModel.findOne({ email: reqBody.email });

    if (user) {
      user.reset_password_token = generateNumber(6);

      await user.save();

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.SEND_RESET_PASSWORD_TOKEN, {
        to: user.email,
        subject: `Your password reset token is ${user.reset_password_token} 🔒`,
        data: {
          email: user.email,
          code: user.reset_password_token,
          name: user.name.split(' ')[0],
        },
      });
    }

    // Always return a generic message
    return { message: 'If the email exists, a password reset OTP will be sent' };
  }

  async registerPushNotification(userId: string, subscription: PushNotificationSubscriptionDtoWithCountry) {
    const user = await this.userModel.findById(userId);
    let sub = await this.pushNotificationModel.findOne({ user_id: userId });

    sub = await (await this.pushNotificationModel.create({ user_id: userId, ...subscription })).save();

    if (sub && !user?.onboarding_rewards?.push_notification_credits_earned) {
      //GIVE USER CREDITS FOR ENABLING NOTIFICATIONS
      await this.brokerTransport
        .send(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS_FOR_ONBOARDING_STEPS, {
          step: ONBOARDING_STEPS_WITH_REWARDS.ENABLE_PUSH_NOTIFICATION,
          userId: getDocId(user),
        })
        .toPromise();
    }

    return sub;
  }

  async removePushSubscription(userId: string, subscription: PushNotificationSubscriptionDto) {
    let sub = await this.pushNotificationModel.deleteOne({
      endpoint: subscription.endpoint,
    });

    // sub = await (await this.pushNotificationModel.create({ user_id: userId, ...subscription })).save();

    return sub;
  }

  // Register Firebase Subscription
  async registerFirebaseSubscription(userId: string, subscription: FirebaseSubscriptionDto) {
    let sub = await this.firebaseSubscriptionModel.findOne({ user: userId, fcm_token: subscription.fcm_token });

    if (!sub) {
      sub = await this.firebaseSubscriptionModel.create({ user: userId, ...subscription });
      await sub.save();
    }

    return sub;
  }

  // Remove Firebase Subscription
  async removeFirebaseSubscription(userId: string, subscription: FirebaseSubscriptionDto) {
    const result = await this.firebaseSubscriptionModel.deleteOne({
      user: userId,
      fcm_token: subscription.fcm_token,
    });

    return result;
  }

  async notifyAllUsers(
    message: { title: string; message: string; path: string; data?: Record<string, any> },
    notification_type: NOTIFICATION_TYPE = NOTIFICATION_TYPE.GENERIC,
  ) {
    // TODO: Implement bulk notification sending in NotificationService
    // For now, we'll use a paginated approach to avoid memory issues

    if (this.notificationService) {
      this.logger.log('Using notification service for bulk notifications');

      // We'll need to implement a more efficient method in the future
      // For now, just log that this would be sending notifications to all users
      this.logger.log(`Would send notification to all users: ${message.title}`);

      // Get all users in batches and send notifications
      const pageSize = 100;
      let page = 1;
      let hasMoreUsers = true;
      const results = [];

      while (hasMoreUsers) {
        const users = await this.userModel
          .find()
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .select('_id')
          .lean();

        if (users.length === 0) {
          hasMoreUsers = false;
          break;
        }

        // Send notifications to this batch of users
        for (const user of users) {
          try {
            // For global notifications, we don't include store context as it's meant to be system-wide
            // Users will see these in all their stores
            const notification = await this.notificationService.sendNotification(user._id, {
              title: message.title,
              message: message.message,
              path: message.path,
              type: notification_type,
              data: message.data || {},
              store: undefined, // Explicitly set to undefined for global notifications
            });

            results.push({
              userId: user._id,
              status: 'fulfilled',
              notificationId: notification._id,
            });
          } catch (error) {
            this.logger.error(`Failed to send notification to user ${user._id}: ${error.message}`);
            results.push({
              userId: user._id,
              status: 'rejected',
              error: error.message,
            });
          }
        }

        page++;
      }

      return results;
    }

    // Legacy method (deprecated)
    this.logger.warn('Using deprecated notification sending method. Please update to use NotificationService.');

    const web_push_destinations = await this.pushNotificationModel.find({});
    const firebase_destinations = await this.firebaseSubscriptionModel.find({});

    return await sendNotification(web_push_destinations, firebase_destinations, {
      title: message.title,
      message: message.message,
      path: message.path,
      type: notification_type,
      data: message.data,
    });
  }

  async notifyUsers(message: NotificationInfoDto) {
    // Handle single email
    console.log('message', { message });
    if (message.user_email) {
      return await this.handleSingleEmailNotification(message);
    }

    // Handle multiple emails
    if (message.emails && message.emails.length > 0) {
      return await this.handleMultipleEmailsNotification(message);
    }

    // Handle country-specific notifications
    if (message.country) {
      return await this.handleCountryNotification(message);
    }

    // Handle broadcast to all users
    return await this.handleBroadcastNotification(message);
  }

  private async handleSingleEmailNotification(message: NotificationInfoDto) {
    const user = await this.userModel.findOne({ email: message.user_email }).populate('stores');

    if (!user) {
      throw new BadRequestException("Couldn't find user with email");
    }

    // Get user's primary store if available
    const storeId = user.primary_store?.toString() || user.stores?.[0]?.toString();

    // If we have the notification service, use it
    if (this.notificationService) {
      const notification = await this.notificationService.sendNotification(user.id, {
        title: message.title,
        message: message.message,
        path: message.path,
        type: message.type || NOTIFICATION_TYPE.GENERIC,
        data: message.data,
        store: storeId,
      });

      return {
        totalSent: 1,
        totalSuccessful: 1,
        result: [{ userId: user.id, email: user.email, status: 'fulfilled', notificationId: notification._id }],
      };
    }

    // Legacy method for single user
    const web_push_destinations = await this.pushNotificationModel.find({ user_id: user.id });
    const firebase_destinations = await this.firebaseSubscriptionModel.find({ user: user.id });

    const result = await sendNotification(
      web_push_destinations,
      firebase_destinations,
      {
        title: message.title,
        message: message.message,
        path: message.path,
        type: message.type || NOTIFICATION_TYPE.GENERIC,
        data: message.data,
      },
      this.notificationService,
      user.id,
      storeId,
    );

    const successfulNotifications = result.filter((r) => r.status === 'fulfilled');
    return { totalSent: result.length, totalSuccessful: successfulNotifications.length, result };
  }

  private async handleMultipleEmailsNotification(message: NotificationInfoDto) {
    const users = await this.userModel.find({ email: { $in: message.emails } }).populate('stores');

    if (users.length === 0) {
      throw new BadRequestException('No users found with the provided emails');
    }

    // If we have the notification service, use it for each user
    if (this.notificationService) {
      const results = [];
      let totalSuccessful = 0;

      for (const user of users) {
        try {
          const userStoreId = user.primary_store?.toString() || user.stores?.[0]?.toString();

          const notification = await this.notificationService.sendNotification(user.id, {
            title: message.title,
            message: message.message,
            path: message.path,
            type: message.type || NOTIFICATION_TYPE.GENERIC,
            data: message.data,
            store: userStoreId,
          });

          results.push({
            userId: user.id,
            email: user.email,
            status: 'fulfilled',
            notificationId: notification._id,
          });
          totalSuccessful++;
        } catch (error) {
          this.logger.error(`Failed to send notification to user ${user.id} (${user.email}): ${error.message}`);
          results.push({
            userId: user.id,
            email: user.email,
            status: 'rejected',
            error: error.message,
          });
        }
      }

      return {
        totalSent: users.length,
        totalSuccessful,
        result: results,
      };
    }

    // Legacy method for multiple users
    const userIds = users.map((user) => user.id);
    const web_push_destinations = await this.pushNotificationModel.find({ user_id: { $in: userIds } });
    const firebase_destinations = await this.firebaseSubscriptionModel.find({ user: { $in: userIds } });

    const result = await sendNotification(
      web_push_destinations,
      firebase_destinations,
      {
        title: message.title,
        message: message.message,
        path: message.path,
        type: message.type || NOTIFICATION_TYPE.GENERIC,
        data: message.data,
      },
      this.notificationService,
      undefined, // no single userId for multiple users
      undefined, // no single storeId for multiple users
    );

    const successfulNotifications = result.filter((r) => r.status === 'fulfilled');
    return { totalSent: result.length, totalSuccessful: successfulNotifications.length, result };
  }

  private async handleCountryNotification(message: NotificationInfoDto) {
    if (this.notificationService) {
      // Use the modern NotificationService approach
      // Find users who have push notification subscriptions in the specified country
      const web_push_destinations = await this.pushNotificationModel.find({ country: message.country });
      const firebase_destinations = await this.firebaseSubscriptionModel.find({ country: message.country });

      // Get unique user IDs from both push and firebase subscriptions
      const webPushUserIds = web_push_destinations.map((sub) => sub.user_id);
      const firebaseUserIds = firebase_destinations.map((sub) => sub.user.toString());
      const allUserIds = [...new Set([...webPushUserIds, ...firebaseUserIds])];

      const results = [];
      let totalSent = 0;
      let totalSuccessful = 0;

      for (const userId of allUserIds) {
        try {
          // Get user to find their primary store
          const user = await this.userModel.findById(userId).select('primary_store stores email').lean();
          if (!user) continue;

          const storeId = user.primary_store?.toString() || user.stores?.[0]?.toString();

          const notification = await this.notificationService.sendNotification(userId, {
            title: message.title,
            message: message.message,
            path: message.path,
            type: message.type || NOTIFICATION_TYPE.GENERIC,
            data: message.data,
            store: storeId,
          });

          results.push({
            userId: userId,
            email: user.email,
            status: 'fulfilled',
            notificationId: notification._id,
          });
          totalSuccessful++;
        } catch (error) {
          this.logger.error(`Failed to send notification to user ${userId}: ${error.message}`);
          results.push({
            userId: userId,
            status: 'rejected',
            error: error.message,
          });
        }
        totalSent++;
      }

      return { totalSent, totalSuccessful, result: results };
    }

    // Fallback to legacy method if NotificationService is not available
    this.logger.warn('NotificationService not available, falling back to legacy method for country notifications');
    const web_push_destinations = await this.pushNotificationModel.find({ country: message.country });
    const firebase_destinations = await this.firebaseSubscriptionModel.find({ country: message.country });

    const result = await sendNotification(
      web_push_destinations,
      firebase_destinations,
      {
        title: message.title,
        message: message.message,
        path: message.path,
        type: message.type || NOTIFICATION_TYPE.GENERIC,
        data: message.data,
      },
      this.notificationService,
      undefined,
      undefined,
    );

    const successfulNotifications = result.filter((r) => r.status === 'fulfilled');
    return { totalSent: result.length, totalSuccessful: successfulNotifications.length, result };
  }

  private async handleBroadcastNotification(message: NotificationInfoDto) {
    if (this.notificationService) {
      // Use the modern NotificationService approach similar to notifyAllUsers
      const pageSize = 100;
      let page = 1;
      let hasMoreUsers = true;
      const results = [];
      let totalSent = 0;
      let totalSuccessful = 0;

      while (hasMoreUsers) {
        const users = await this.userModel
          .find()
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .select('_id email primary_store stores')
          .lean();

        if (users.length === 0) {
          hasMoreUsers = false;
          break;
        }

        // Send notifications to this batch of users
        for (const user of users) {
          try {
            const storeId = user.primary_store?.toString() || user.stores?.[0]?.toString();

            const notification = await this.notificationService.sendNotification(user._id, {
              title: message.title,
              message: message.message,
              path: message.path,
              type: message.type || NOTIFICATION_TYPE.GENERIC,
              data: message.data,
              store: storeId,
            });

            results.push({
              userId: user._id,
              email: user.email,
              status: 'fulfilled',
              notificationId: notification._id,
            });
            totalSuccessful++;
          } catch (error) {
            this.logger.error(`Failed to send notification to user ${user._id}: ${error.message}`);
            results.push({
              userId: user._id,
              email: user.email,
              status: 'rejected',
              error: error.message,
            });
          }
          totalSent++;
        }

        page++;
      }

      return { totalSent, totalSuccessful, result: results };
    }

    // Fallback to legacy method if NotificationService is not available
    this.logger.warn('NotificationService not available, falling back to legacy method for broadcast notifications');
    const web_push_destinations = await this.pushNotificationModel.find({});
    const firebase_destinations = await this.firebaseSubscriptionModel.find({});

    const result = await sendNotification(
      web_push_destinations,
      firebase_destinations,
      {
        title: message.title,
        message: message.message,
        path: message.path,
        type: message.type || NOTIFICATION_TYPE.GENERIC,
        data: message.data,
      },
      this.notificationService,
      undefined,
      undefined,
    );

    const successfulNotifications = result.filter((r) => r.status === 'fulfilled');
    return { totalSent: result.length, totalSuccessful: successfulNotifications.length, result };
  }

  async addStore(userId: string, storeId: string, isPrimaryStore: boolean) {
    const user = await this.userModel
      .findByIdAndUpdate(
        userId,
        {
          $addToSet: { stores: storeId as any },
          ...(isPrimaryStore ? { primary_store: storeId as any } : {}),
        },
        { useFindAndModify: false, new: true },
      )
      .populate('stores');

    this.checkUser(user);

    return user;
  }

  async redeemReferralCredits(userId: string, country: COUNTRY_CODE) {
    const user = await this.userModel.findById(userId);

    if (!user) return;

    const startingBalance =
      user?.referred_by && user?.stores?.length === 1 ? CREDITS.REFERRAL.COMMISION.SIGNUP[country] : 0;

    const creditRes = await this.creditsService.addCreditsToUser(
      userId,
      startingBalance,
      { referred_by: user?.referred_by },
      'Referral credits earned',
      country,
    );

    return creditRes.credits;
  }

  async removeStore(userId: string, storeId: string) {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        $pull: { stores: storeId as any },
      },
      { useFindAndModify: false, new: true },
    );

    this.checkUser(user);
    return user;
  }
  async verifyPassword(userId: string, password: string) {
    const user = await this.userModel.findById(userId);
    if (!user) return false;
    const correctPassword = await bcrypt.compare(password, user.password);
    return correctPassword;
  }

  async resetPassword(req: ResetPasswordUserDto) {
    const user = await this.userModel.findOne({
      reset_password_token: req.token,
    });
    if (!user) {
      throw new BadRequestException('Invalid password reset token');
    }

    user.password = bcrypt.hashSync(req.password, 10);
    await user.save();

    return {
      message: 'Password reset successfully',
    };
  }

  async getUsers(paginationQuery: PaginatedQueryDto, filter: FilterUserDto) {
    // return this.userModel.find({}, 'email name phone');
    const matchQuery = filter?.search
      ? {
          $or: [{ name: new RegExp(filter.search, 'ig') }, { email: new RegExp(filter.search, 'ig') }],
        }
      : {};

    const usersCount = await this.userModel.countDocuments(matchQuery);
    let users: any = await this.userModel.aggregate([
      {
        $match: { ...matchQuery, reference: { $exists: false } },
      },
      {
        $skip:
          (paginationQuery.page ? paginationQuery.page - 1 : 0) *
          (paginationQuery.per_page ? paginationQuery.per_page : 25),
      },
      { $limit: paginationQuery.per_page ? paginationQuery.per_page : 25 },
      {
        $lookup: {
          from: 'stores',
          localField: '_id',
          foreignField: 'owner',
          as: 'stores',
        },
      },
      {
        $facet: {
          metadata: [
            { $count: 'users' },
            {
              $addFields: {
                page: paginationQuery.page - 1 || 1,
                total_users: usersCount,
              },
            },
          ],
          data: [
            {
              $project: {
                stores: { name: 1, logo: 1, slug: 1 },
                email: 1,
                name: 1,
                phone: 1,
                createdAt: 1,
              },
            },
          ], // add projection here wish you re-shape the docs
        },
      },
      { $sort: { created_at: paginationQuery?.sort === 'ASC' ? -1 : 1 } },
      {
        $facet: {
          metadata: [{ $count: 'total_users' }, { $addFields: { page: paginationQuery.page || 1 } }],
          data: [
            // {
            //   $skip: (paginationQuery.page - 1 || 0) * (paginationQuery.per_page || 400),
            // },
            // { $limit: paginationQuery.per_page || 400 },
          ], // add projection here wish you re-shape the docs
        },
      },
    ]);

    users = users[0];
    users.metadata = users.metadata[0];

    return users;
  }

  async aggregateUser(filter: any[]) {
    return this.userModel.aggregate(filter);
  }

  deleteUser(id: string) {
    return this.userModel.findByIdAndRemove(id);
  }

  async migrateUserPhoneNumbers() {
    const users = await this.userModel.find({});

    for (const user of users) {
      if (user.phone.startsWith('234')) {
        user.phone = '+234-' + user.phone.slice(3);
      } else if (user.phone.startsWith('0')) {
        user.phone = '+234-' + user.phone.slice(1);
      }

      await user.save();
    }
  }

  async updateUserSubscription(userId: Types.ObjectId, data: UpdateUserSubscriptionDto) {
    let subscription: Subscription;

    subscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, {
        owner: userId,
      })
      .toPromise();

    subscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
        filter: { owner: userId },
        update: {
          status:
            data.status !== undefined
              ? data.status
                ? SUBSCRIPTION_STATUS.ACTIVE
                : SUBSCRIPTION_STATUS.IN_ACTIVE
              : subscription.status,
          next_payment_date: data.next_payment_date ?? subscription.next_payment_date,
          plan: data.plan ?? subscription.plan,
        },
      })
      .toPromise();

    return subscription;
  }

  async adminGetUser(userId: string) {
    const user = await this.userModel.findById(userId).populate({
      path: 'primary_store',
      populate: { path: 'subscription', populate: [{ path: 'plan' }, { path: 'plan_option' }] },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    return user;
  }

  async markUserAsQualified(userId: string) {
    const user = await this.userModel.findById(userId);

    try {
      await this.sendConversionEventToMeta(userId, 'StartTrial', user.id);
    } catch (error) {}

    user.meta.is_qualified = true;
    await user.save();

    return user;
  }

  async sendConversionEventToMeta(userId: string, eventName: string, eventId: string) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user?.source_ad?.utm_campaign) {
      const metaConfig = this.config.get<MetaConfig>('metaConfiguration');

      const firstName = user?.name?.split(' ')[0] ?? '';
      const lastName = user?.name?.split(' ')[1] ?? '';

      const userData = {
        em: [hashForMeta(user.email)],
        ph: [hashForMeta(user.phone.replace(/-/g, ''))],
        fn: firstName ? [hashForMeta(firstName)] : undefined,
        ln: lastName ? [hashForMeta(lastName)] : undefined,
        fbp: user?.source_ad?.fbp,
        fbc: user?.source_ad?.fbc,
        client_ip_address: user?.source_ad?.ip_address,
        client_user_agent: user?.source_ad?.user_agent,
      };

      const payload = {
        data: [
          {
            event_name: eventName,
            event_id: eventId,
            event_time: Math.floor(Date.now() / 1000),
            action_source: 'website',
            event_source_url: `${process.env.CATLOG_APP}/sign-up`,
            user_data: removeNullUndefinedFromObject(userData),
          },
        ],
      };

      //Send Event to Meta
      axios
        .post(
          `https://graph.facebook.com/v18.0/${metaConfig.pixelId}/events?access_token=${metaConfig.conversionApiKey}`,
          payload,
        )
        .then((response) => {
          console.log({ response });
        })
        .catch((error) => {
          throw new BadRequestException(error.response?.data || error);
        });
    }

    return user;
  }

  async createUsersOnCustomerIo() {
    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 6);

    const users = await this.userModel
      .find({
        last_login: { $gte: twoMonthsAgo },
        $or: [{ 'meta.added_to_customer_io': { $exists: false } }, { 'meta.added_to_customer_io': { $ne: true } }],
      })
      .limit(500);

    const reqs = users.map((user) => () => this.createUserOnCustomerIo(user));
    await Promise.all(reqs.map((req) => req()));

    return {
      message: `Created ${users.length} users on Customer.io`,
    };
  }

  async updateMailchimpList() {
    const users = await this.userModel.find({ reference: { $exists: false } }).lean(true);
    let res = [];

    for (const user of users) {
      const userExists = await this.brevo.userExists(user.email);
      let recentlyUpdated = false;

      const userData = userExists?.body as GetExtendedContactDetails;

      if (userData && userData?.modifiedAt) {
        const currentTime = dayjs(); // Current time
        const timeDifferenceMinutes = currentTime.diff(dayjs(userData?.modifiedAt), 'minutes');

        recentlyUpdated = timeDifferenceMinutes < 15;
      }

      if (recentlyUpdated) {
        console.log('SKIPPING FOR: ' + user.email);

        continue;
      }

      console.log('RUNNING FOR: ' + user.email);

      let products = 0;

      for (const store of user.stores) {
        const storeProducts = await this.brokerTransport
          .send<number>(BROKER_PATTERNS.ITEM.GET_STORE_ITEMS_COUNT, store)
          .toPromise();
        products += storeProducts;
      }

      try {
        const country = user?.phone.startsWith('+233') ? COUNTRY_CODE.GH : COUNTRY_CODE.NG;

        if (userExists) {
          await this.brevo.generalUpdate(user.email, {
            FIRSTNAME: user.name.split(' ')[0] ?? '',
            LASTNAME: user.name.split(' ')[1] ?? '',
            COUNTRY: country,
            PRODUCTS_COUNT: products,
          });
        } else {
          await this.brevo.createUser(user.email, user.name.split(' ')[0], user.name.split(' ')[1], country, products);
        }

        res.push({ ...user, products });
      } catch (e) {
        this.logger.error(e);
      }
    }

    return res;
  }

  async migratePrimaryStore() {
    const per_page = 2000;

    // Fetch users who have at least one store but no primary_store set, up to 2000 records
    const results = await this.userModel
      .find({
        stores: { $exists: true, $not: { $size: 0 } },
        $or: [{ primary_store: { $exists: false } }, { primary_store: null }],
      })
      .limit(per_page)
      .lean()
      .select('stores');

    const users = results;

    if (users.length === 0) {
      return {
        message: 'No more users to update',
        data: [], // No users updated
      };
    }

    const usersUpdateReq = users.map(async (user) => {
      let update;

      if (user.stores.length === 1) {
        // If the user has only one store, set it as primary_store
        update = this.userModel.findByIdAndUpdate(user._id, { primary_store: user.stores[0] });
      } else {
        // If the user has more than one store, find the first one created
        const firstStore = await this.brokerTransport
          .send<Store>(BROKER_PATTERNS.STORE.GET_FIRST_STORE, {
            owner: user._id,
          })
          .toPromise();
        update = this.userModel.updateOne({ _id: user._id }, { primary_store: firstStore });
      }

      return update;
    });

    const updatedUsers = await Promise.all(usersUpdateReq);

    return {
      message: `Updated ${users.length} users. Trigger the process again to update more.`,
    };
  }

  async migrateUsersSub() {
    const users = await this.userModel.find({ reference: { $exists: true } }).lean(true);
    // const subs = await this.brokerTransport
    //   .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_MULTIPLE_SUBSCRIPTIONS, {
    //     filter: { data: true },
    //     withUser: true,
    //   })
    //   .toPromise();
    // const subsOwners = subs.map((s) => String(s.owner)) as string[];
    let res = [];

    // return { subsOwners };

    for (const user of users) {
      res.push(
        this.brokerTransport
          .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
            filter: { owner: user?._id, data: { $ne: true } },
            update: { data: true },
          })
          .toPromise(),
      );
    }

    const data = await Promise.all(res);

    return data;
  }

  async migratePushNotificationCountry() {
    // Fetch all PushNotificationSubscription documents and populate the corresponding User and primary_store
    const subscriptions = await this.pushNotificationModel
      .aggregate([
        {
          $lookup: {
            from: 'users',
            let: { user_id: { $toObjectId: '$user_id' } },
            pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$user_id'] } } }],
            as: 'user',
          },
        },
        { $unwind: '$user' },
        {
          $lookup: {
            from: 'stores',
            let: { primary_store: { $toObjectId: '$user.primary_store' } },
            pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$primary_store'] } } }],
            as: 'primary_store',
          },
        },
        { $unwind: '$primary_store' },
      ])
      .exec();

    // Prepare the bulk update operations
    const bulkOps = subscriptions.map((subscription) => {
      return {
        updateOne: {
          filter: { _id: subscription._id },
          update: { $set: { country: subscription.primary_store.country } },
        },
      };
    });

    // Perform the bulk update
    if (bulkOps.length > 0) {
      await this.pushNotificationModel.bulkWrite(bulkOps);
    }

    this.logger.log('PushNotificationSubscriptions updated successfully.');

    return {
      message: `Updated ${bulkOps.length} push notification subscriptions successfully.`,
    };
  }

  async getPhoneCSV(country: string) {
    const countryMap = {
      [COUNTRY_CODE.NG]: { code: '+234', length: 14 },
      [COUNTRY_CODE.GH]: { code: '+233', length: 13 },
      [COUNTRY_CODE.KE]: { code: '+254', length: 13 },
      [COUNTRY_CODE.ZA]: { code: '+27', length: 12 },
    };

    if (!countryMap[country]) throw new BadRequestException('Invalid Country');

    const users = await this.userModel
      .find({ reference: { $exists: false }, phone: { $regex: `^\\+${countryMap[country]?.code}` } }, { phone: 1 })
      .lean();

    const csvString = users.reduce((cumm, user) => {
      const formattedPhone = user.phone.replace(/-/g, '');
      const canAdd = formattedPhone.length === countryMap[country].length;

      return canAdd ? cumm + `\n${formattedPhone}` : cumm;
    }, `Phone number`);

    return Buffer.from(csvString, 'utf-8');
  }

  async getUsersBatch(skip: number, limit: number) {
    const users = await this.userModel.find().skip(skip).limit(limit).lean();

    return users;
  }

  /**
   * Changes the country for a user and updates all related entities
   * @param userId The ID of the user
   * @param newCountry The new country code
   * @returns Updated user information
   */
  async changeUserCountry(userId: string, newCountry: COUNTRY_CODE) {
    const user = await this.getAndValidateUser(userId);

    // Get all stores owned by the user
    const stores = await this.brokerTransport
      .send<Store[]>(BROKER_PATTERNS.STORE.GET_STORES_LEAN, { filter: { owner: userId } })
      .toPromise();

    if (!stores || stores.length === 0) {
      throw new NotFoundException('No stores found for this user');
    }

    // Get the appropriate currency for the new country
    const newCurrency = COUNTRY_CURRENCY_MAP[newCountry];

    // Transaction changes to be performed
    let updatedUser = false;
    let updatedStores = 0;
    let updatedWallets = 0;
    let updatedSubscriptions = 0;
    const errors = [];

    // 1. Update push notification subscriptions for the user
    try {
      await this.pushNotificationModel.updateMany({ user_id: userId }, { country: newCountry });
    } catch (error) {
      this.logger.warn(`Failed to update push notifications for user ${userId}: ${error.message}`);
      errors.push({ type: 'push_notifications', message: error.message });
      // Continue with other updates
    }

    // 2. Update each store and related data
    for (const store of stores) {
      const storeId = getDocId(store);

      // 2.1 Update store country
      try {
        await this.brokerTransport
          .send(BROKER_PATTERNS.STORE.UPDATE_STORE, {
            filter: { _id: storeId },
            update: { country: newCountry },
          })
          .toPromise();
        updatedStores++;
      } catch (error) {
        this.logger.warn(`Failed to update country for store ${storeId}: ${error.message}`);
        errors.push({ type: 'store_country', storeId, message: error.message });
        // Continue with other updates for this store
      }

      // 2.2 Update store currencies settings
      try {
        await this.brokerTransport
          .send(BROKER_PATTERNS.STORE.UPDATE_STORE, {
            filter: { _id: storeId },
            update: {
              currencies: {
                default: newCurrency,
                products: newCurrency,
                storefront: [newCurrency],
                storefront_default: newCurrency,
                rates: {},
              },
            },
          })
          .toPromise();
      } catch (error) {
        this.logger.warn(`Failed to update currencies for store ${storeId}: ${error.message}`);
        errors.push({ type: 'store_currencies', storeId, message: error.message });
        // Continue with other updates for this store
      }

      // 2.3 Update wallet currencies
      try {
        const wallets = await this.brokerTransport
          .send<Wallet[]>(BROKER_PATTERNS.WALLET.GET_WALLETS, { store: storeId })
          .toPromise();

        if (wallets && wallets.length > 0) {
          try {
            // First, update the actual wallet's currency - this broker method uses walletId and currency directly
            await this.brokerTransport
              .send(BROKER_PATTERNS.WALLET.UPDATE_WALLET_CURRENCY, {
                walletId: getDocId(wallets[0]),
                currency: newCurrency,
              })
              .toPromise();

            // Then, update the store's wallets array
            try {
              const store = await this.brokerTransport
                .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
                .toPromise();

              if (store && store.wallets && store.wallets.length > 0) {
                const updatedWallets = [...store.wallets];
                updatedWallets[0] = {
                  ...updatedWallets[0],
                  currency: newCurrency,
                };

                await this.brokerTransport
                  .send(BROKER_PATTERNS.STORE.UPDATE_STORE, {
                    filter: { _id: storeId },
                    update: {
                      wallets: updatedWallets,
                    },
                  })
                  .toPromise();
              } else {
                // If no wallets exist in the store reference yet, create one
                await this.brokerTransport
                  .send(BROKER_PATTERNS.STORE.UPDATE_STORE, {
                    filter: { _id: storeId },
                    update: {
                      wallets: [{ currency: newCurrency, id: getDocId(wallets[0]) }],
                    },
                  })
                  .toPromise();
              }
            } catch (storeUpdateError) {
              this.logger.warn(`Failed to update wallets array in store ${storeId}: ${storeUpdateError.message}`);
              errors.push({ type: 'store_wallets_array', storeId, message: storeUpdateError.message });
              // Continue with other updates for this store
            }

            updatedWallets++;
          } catch (walletUpdateError) {
            if (
              walletUpdateError.message &&
              walletUpdateError.message.includes('Cannot change wallet currency when balance is not zero')
            ) {
              this.logger.warn(`Could not update wallet currency for store ${storeId}: ${walletUpdateError.message}`);
              errors.push({ type: 'wallet_currency', storeId, message: walletUpdateError.message });

              // Still update the store's currency settings
              try {
                await this.brokerTransport
                  .send(BROKER_PATTERNS.STORE.UPDATE_STORE, {
                    filter: { _id: storeId },
                    update: {
                      currencies: {
                        default: newCurrency,
                        products: newCurrency,
                        storefront: [newCurrency],
                        storefront_default: newCurrency,
                        rates: {},
                      },
                    },
                  })
                  .toPromise();
              } catch (currencyUpdateError) {
                this.logger.warn(
                  `Failed to update currency settings for store ${storeId}: ${currencyUpdateError.message}`,
                );
                errors.push({ type: 'store_currencies_fallback', storeId, message: currencyUpdateError.message });
              }
            } else {
              this.logger.warn(`Failed to update wallet for store ${storeId}: ${walletUpdateError.message}`);
              errors.push({ type: 'wallet_general', storeId, message: walletUpdateError.message });
            }
            // Continue with other updates for this store
          }
        }
      } catch (walletsGetError) {
        this.logger.warn(`Failed to get wallets for store ${storeId}: ${walletsGetError.message}`);
        errors.push({ type: 'get_wallets', storeId, message: walletsGetError.message });
        // Continue with other updates for this store
      }

      // 2.4 Update subscriptions and plan options
      try {
        const subscription = await this.brokerTransport
          .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, { owner: userId })
          .toPromise();

        if (subscription) {
          try {
            // Find a plan option for the new country
            const planOption = await this.brokerTransport
              .send<PlanOption>(BROKER_PATTERNS.PLAN.GET_PLAN_OPTION, {
                filter: {
                  plan_type: subscription.initial_plan || PLAN_TYPE.BASIC,
                  country: newCountry,
                },
              })
              .toPromise();

            if (planOption) {
              await this.brokerTransport
                .send(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
                  filter: { _id: getDocId(subscription) },
                  update: { plan_option: getDocId(planOption) },
                })
                .toPromise();
              updatedSubscriptions++;
            }
          } catch (planUpdateError) {
            this.logger.warn(`Failed to update subscription plan for user ${userId}: ${planUpdateError.message}`);
            errors.push({ type: 'subscription_plan', message: planUpdateError.message });
            // Continue with other updates for this store
          }
        }
      } catch (subscriptionGetError) {
        this.logger.warn(`Failed to get subscription for user ${userId}: ${subscriptionGetError.message}`);
        errors.push({ type: 'get_subscription', message: subscriptionGetError.message });
        // Continue with other updates for this store
      }

      // 2.5 Update Catlog credits currency
      try {
        await this.creditsService.changeCurrencyForStore(storeId, newCurrency);
      } catch (creditsUpdateError) {
        this.logger.warn(`Failed to update credits currency for store ${storeId}: ${creditsUpdateError.message}`);
        errors.push({ type: 'credits_currency', storeId, message: creditsUpdateError.message });
        // Continue with other updates for this store
      }
    }

    // 3. Update user-level data if any
    if (user.primary_store) {
      updatedUser = true;
    }

    // 4. Update customer.io data
    try {
      await this.customerIo.createOrUpdateUser({
        id: userId,
        country: newCountry,
      });
    } catch (customerIoError) {
      this.logger.warn(`Failed to update customer.io for user ${userId}: ${customerIoError.message}`);
      errors.push({ type: 'customer_io', message: customerIoError.message });
      // Continue to return the result
    }

    return {
      message: 'Country updated successfully',
      details: {
        user: {
          id: userId,
          country: newCountry,
          updated: updatedUser,
        },
        stats: {
          stores_updated: updatedStores,
          wallets_updated: updatedWallets,
          subscriptions_updated: updatedSubscriptions,
          errors: errors.length > 0 ? errors : undefined,
        },
      },
    };
  }

  /**
   * Assigns an internal dashboard role to a user
   * @param userId The ID of the user to assign a role to
   * @param role The internal dashboard role to assign
   */
  async assignInternalDashboardRole(userId: string, role: string) {
    const user = await this.userModel.findById(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if the role is valid
    if (!Object.values(INTERNAL_ROLES).includes(role as any)) {
      throw new BadRequestException('Invalid internal dashboard role');
    }

    user.internal_dashboard_role = role;
    await user.save();

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.internal_dashboard_role,
    };
  }

  /**
   * Removes an internal dashboard role from a user
   * @param userId The ID of the user to remove the role from
   */
  async removeInternalDashboardRole(userId: string) {
    const user = await this.userModel.findById(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.internal_dashboard_role = undefined;
    await user.save();

    return {
      id: user.id,
      name: user.name,
      email: user.email,
    };
  }

  /**
   * Gets all users with internal dashboard roles
   */
  async getInternalDashboardUsers() {
    const users = await this.userModel.find(
      { internal_dashboard_role: { $exists: true, $ne: null } },
      'name email internal_dashboard_role',
    );

    return users;
  }

  async getUserByEmail(email: string): Promise<UserDocument> {
    const user = await this.userModel.findOne({ email });
    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }
    return user;
  }

  async sendPushNotification(
    storeId: string,
    message: { title: string; message: string; path: string },
    owner_only: boolean = false,
    user?: string,
    notification_type: NOTIFICATION_TYPE = NOTIFICATION_TYPE.GENERIC,
    data?: Record<string, any>,
  ) {
    // Array to collect results
    const results = [];
    let receivers: string[] = [];

    // Get store and receivers
    if (storeId) {
      try {
        const store = await this.brokerTransport
          .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
          .toPromise();

        if (!store) {
          this.logger.error(`Store not found with ID: ${storeId}`);
          return [];
        }

        // Fix the owner/owners handling:
        const ownerStr = store.owner?.toString() || '';
        const ownerUsers = Array.isArray(store.owners)
          ? store.owners.map((o) => o.user?.toString() || '').filter(Boolean)
          : [];

        receivers = owner_only ? [ownerStr] : [ownerStr, ...ownerUsers];

        // Remove duplicates if any
        receivers = [...new Set(receivers)];
      } catch (error) {
        this.logger.error(`Error retrieving store with ID ${storeId}: ${error.message}`);
        return [];
      }
    }

    // If specific user is provided, use that instead
    if (user) {
      receivers = [user];
    }

    // If no receivers found, return empty results
    if (receivers.length === 0) {
      return [];
    }

    // We have notificationService, use it for each receiver
    if (this.notificationService) {
      for (const receiverId of receivers) {
        try {
          const notification = await this.notificationService.sendNotification(receiverId, {
            title: message.title,
            message: message.message,
            path: message.path,
            type: notification_type,
            data: data || {},
            store: storeId, // Include store ID in notification
          });

          results.push({
            userId: receiverId,
            status: 'fulfilled',
            notificationId: notification._id,
          });
        } catch (error) {
          this.logger.error(`Failed to send notification to user ${receiverId}: ${error.message}`);
          results.push({
            userId: receiverId,
            status: 'rejected',
            error: error.message,
          });
        }
      }

      return results;
    }

    // Fallback to legacy method if notification service is not available
    // This code path will be removed soon when all parts of the application use the notification service
    this.logger.warn('Using deprecated notification sending method. Please update to use NotificationService.');

    // Fetch Web Push Subscriptions
    const web_push_destinations = await this.pushNotificationModel.find({
      user_id: { $in: receivers },
    });

    // Fetch Firebase Subscriptions
    const firebase_destinations = await this.firebaseSubscriptionModel.find({
      user: { $in: receivers },
    });

    // Use legacy method
    return await sendNotification(web_push_destinations, firebase_destinations, {
      title: message.title,
      message: message.message,
      path: message.path,
      type: notification_type,
      data: data || {},
    });
  }

  /**
   * Get the current global mobile app signin counter value
   */
  async getMobileAppSigninCounter(): Promise<number> {
    const config = await this.adminConfigService.getConfig(ADMIN_CONFIG.MOBILE_APP_SIGNIN_COUNTER);
    return config ? parseInt(config.value, 10) : 0;
  }

  /**
   * Increment the global mobile app signin counter and return the new position
   */
  async incrementMobileAppSigninCounter(): Promise<number> {
    const currentCounter = await this.getMobileAppSigninCounter();
    const newCounter = currentCounter + 1;

    await this.adminConfigService.updateMobileAppSigninCounter(newCounter);

    return newCounter;
  }

  /**
   * Check if user already has a mobile app signin position
   */
  async userHasMobileAppSigninPosition(userId: string): Promise<boolean> {
    const user = await this.userModel.findById(userId);
    return !!(user?.meta?.mobile_app_signin_position);
  }

  /**
   * Assign mobile app signin position to user
   */
  async assignMobileAppSigninPosition(userId: string, position: number): Promise<void> {
    await this.userModel.findByIdAndUpdate(userId, {
      $set: {
        'meta.mobile_app_signin_position': position
      }
    });
  }

  /**
   * Get user's mobile app signin position
   */
  async getUserMobileAppSigninPosition(userId: string): Promise<number | null> {
    const user = await this.userModel.findById(userId);
    return user?.meta?.mobile_app_signin_position || null;
  }
}
