import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  PreconditionFailedException,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { UserService } from './user.service';
import {
  AdminChangeCountryDto,
  CreateUserDto,
  CreateUserDtoV2,
  FilterUserDto,
  InternalDashboardRoleDto,
  FirebaseSubscriptionDto,
  InternalLoginUserDto,
  LoginUserDto,
  NotificationInfoDto,
  OrderLoginUserDto,
  PushNotificationSubscriptionDto,
  RequestPasswordResetUserDto,
  ResetPasswordUserDto,
  SendVerificationTokenUserDto,
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserSubscriptionDto,
  VerifyTokenUserDto,
  ChangeCountryDto,
} from '../../models/dtos/UserDtos';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { UserCreatedResponse } from '../../interfaces/user.interface';
import { User, UserDocument } from './user.schema';
import { FirebaseSubscription } from './notifications/notification.schema';
import { MessageResponseDto } from '../../models/dtos/generic.dto';
import { Types } from 'mongoose';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { Readable } from 'stream';
import { IPLookUpRepository } from '../../repositories/iplookup.repository';
import { Throttle } from '@nestjs/throttler';
import { UserMessagingRepository } from '../../repositories/user-messaging.repository';
import { ONE_MINUTE } from '../../utils/constants';
import { getAppEnv } from '../../utils';
import { COUNTRY_CODE } from '../country/country.schema';
import { INTERNAL_ROLES } from '../../utils/permissions.util';
import { InternalDashboardPermissions } from '../../decorators/permission.decorator';
import { SCOPES } from '../../utils/permissions.util';

@Controller('users')
@ApiTags('User')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly messagingRepository: UserMessagingRepository,
    private readonly ipLookupRepository: IPLookUpRepository,
  ) {}

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Post('')
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({
    status: HttpStatus.CREATED,
    type: UserCreatedResponse,
  })
  async create(@Body() userReq: CreateUserDto, @Req() req: IRequest) {
    // Create a new user request with IP and user agent in sourceAd
    const ip = req.headers['x-real-ip'] as string;
    const userRequestWithSourceAd = {
      ...userReq,
      source_ad: {
        ...(userReq.source_ad || {}),
        ip_address: ip ? ip : getAppEnv() === 'local' ? '**************' : undefined,
        user_agent: req.headers['user-agent'],
      },
    };

    const response = await this.userService.create(userRequestWithSourceAd);

    const user = response?.user;
    const token = await this.userService.generateJwtToken(user);

    return {
      user,
      token,
    };
  }

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Post('/v2')
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({
    status: HttpStatus.CREATED,
    type: UserCreatedResponse,
  })
  async createUserV2(@Body() userReq: CreateUserDtoV2, @Req() req: IRequest) {
    // Create a new user request with IP and user agent in sourceAd
    const ip = req.headers['x-real-ip'] as string;
    const userRequestWithSourceAd = {
      ...userReq,
      source_ad: {
        ...(userReq.source_ad || {}),
        ip_address: ip ? ip : getAppEnv() === 'local' ? '**************' : undefined,
        user_agent: req.headers['user-agent'],
      },
    };

    const response = await this.userService.createUserV2(userRequestWithSourceAd);

    const user = response?.user;
    const token = await this.userService.generateJwtToken(user);

    return {
      user,
      token,
    };
  }

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: UserCreatedResponse })
  login(@Body() loginPayload: LoginUserDto, @Req() req: IRequest) {
    return this.userService.login(loginPayload, undefined, req);
  }

  @Post('login/order')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: UserCreatedResponse })
  OrderLogin(@Body() loginPayload: OrderLoginUserDto, @Req() req: IRequest) {
    return this.userService.login(loginPayload, loginPayload.order_id, req);
  }

  @Post('login/internal')
  @HttpCode(HttpStatus.OK)
  async internalLogin(@Body() loginReq: InternalLoginUserDto) {
    return this.userService.internalLogin(loginReq.username, loginReq.password);
  }

  @Put('')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  async update(@Req() req: IRequest, @Body() userReq: UpdateUserDto) {
    const user = req.user as User;
    return await this.userService.update(user.id, userReq);
  }

  @Put('/setup-video-watched/:id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  async setupVideoWatched(@Req() req: IRequest, @Param('id') userId: string) {
    const user = req.user as User;

    if (user.id.toString() !== userId) {
      throw new BadRequestException('Invalid params');
    }

    return await this.userService.setupVideoWatched(userId);
  }

  @Get('update-last-login')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async updateLastLogin(@Req() req: IRequest) {
    await this.userService.updateLastLogin(req.user.id);
    return {
      message: 'Last login updated successfully',
    };
  }

  @Get('ip-lookup')
  // @UseGuards(JwtAuthGuard)
  // @ApiSecurity('bearer')
  async lookupIP(@Query('ip') ip: string) {
    if (!ip) {
      throw new HttpException('IP address is required', HttpStatus.BAD_REQUEST);
    }

    const result = await this.ipLookupRepository.getIPInfo(ip);

    if (result.error) {
      throw new HttpException(result.error, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return result.data;
  }

  @Put('password')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async updatePassword(@Req() req: IRequest, @Body() userReq: UpdateUserPasswordDto) {
    const user = req.user as User;
    await this.userService.updatePassword(user.id, userReq);
    return {
      message: 'Password updated successfully!',
    };
  }

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Post('send-verification-tokens')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ status: HttpStatus.OK, type: MessageResponseDto })
  @ApiSecurity('bearer')
  async requestVerificationTokens(@Req() req: IRequest, @Body() body: SendVerificationTokenUserDto) {
    const user = req.user as User;
    await this.userService.sendVerificationTokens(user.id, body);
    return {
      message: 'Verification sent successfully',
    };
  }

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Post('verify-tokens')
  @ApiResponse({ status: HttpStatus.OK, type: UserCreatedResponse })
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    description: 'Verify tokens provided by user for email & phone number',
    summary: 'Verify tokens provided by user for email & phone number',
  })
  async verifyVerificationTokens(@Req() req: IRequest, @Body() body: VerifyTokenUserDto) {
    const user = req.user as User;
    return await this.userService.verifyVerificationTokens(user.id, body);
  }

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Get('/refresh-jwt_token/:store')
  @ApiSecurity('bearer')
  @ApiOkResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        token: {
          type: 'string',
        },
      },
    },
  })
  @UseGuards(JwtAuthGuard)
  async refreshJwtToken(@Req() req: IRequest, @Param('store') store: string) {
    const data = await this.userService.refreshJwtToken(req.user.id, store ?? req.user?.store?.id);
    return {
      message: 'Token refreshed successfully',
      data,
    };
  }

  @Post('register-push-notification')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async registerPushNotification(@Req() req: IRequest, @Body() subscription: PushNotificationSubscriptionDto) {
    const res = await this.userService.registerPushNotification(req.user.id, {
      ...subscription,
      country: req?.user?.store?.country,
    });

    return {
      message: 'Push notification subscription stored',
      data: res,
    };
  }

  @Post('register-fcm-notification')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async registerFcmNotification(@Req() req: IRequest, @Body() subscription: FirebaseSubscriptionDto) {
    const res = await this.userService.registerFirebaseSubscription(req.user.id, {
      ...subscription,
      country: req?.user?.store?.country,
    });

    return {
      message: 'FCM notification subscription stored',
      data: res,
    };
  }

  @Delete('remove-push-subscription')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async unregisterPushNotification(@Req() req: IRequest, @Body() subscription: PushNotificationSubscriptionDto) {
    const res = await this.userService.removePushSubscription(req.user.id, {
      ...subscription,
    });

    return {
      message: 'Push subscription removed successfully',
      data: res,
    };
  }

  @Delete('remove-fcm-subscription')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async unregisterFcmNotification(@Req() req: IRequest, @Body() subscription: FirebaseSubscriptionDto) {
    const res = await this.userService.removeFirebaseSubscription(req.user.id, {
      ...subscription,
    });

    return {
      message: 'FCM subscription removed successfully',
      data: res,
    };
  }

  @Throttle({ default: { limit: 5, ttl: 2 * ONE_MINUTE } })
  @Post('request-password-reset')
  @ApiOperation({
    summary: "Requests for forgot password using user's email",
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async requestPasswordReset(@Body() body: RequestPasswordResetUserDto) {
    return this.userService.requestPasswordReset(body);
  }

  @Get('me')
  @ApiOperation({
    summary: 'Returns the user profile details for a logged in user',
  })
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: UserCreatedResponse })
  async userProfile(@Req() req: IRequest) {
    return this.userService.getUserProfile({ _id: req.user.id });
  }

  @Get('mobile-app-signin-position')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOperation({ summary: 'Get user mobile app signin position' })
  @ApiOkResponse({
    schema: {
      properties: {
        position: {
          type: 'number',
          example: 1,
          description: 'User position in global mobile app signin counter, null if not set'
        },
        message: {
          type: 'string',
          example: "You're the 1st person to download the app"
        }
      }
    }
  })
  async getMobileAppSigninPosition(@Req() req: IRequest) {
    const position = await this.userService.getUserMobileAppSigninPosition(req.user.id);

    if (position === null) {
      return {
        position: null,
        message: null
      };
    }

    const ordinalSuffix = (n: number) => {
      const s = ['th', 'st', 'nd', 'rd'];
      const v = n % 100;
      return n + (s[(v - 20) % 10] || s[v] || s[0]);
    };

    return {
      position,
      message: `You're the ${ordinalSuffix(position)} person to download the app`
    };
  }

  @Post('reset-password')
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async resetPassword(@Body() body: ResetPasswordUserDto) {
    return this.userService.resetPassword(body);
  }

  // @Get('/referrals')
  // @UseGuards(JwtAuthGuard)
  // async getReferrals(@Req() req: IRequest) {
  //   const refs = await this.userService.getReferrals(req.user.id)

  //   return {
  //     message: "Referrals fetched successfully",
  //     data: refs
  //   }
  // }

  @Post('/third-party')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async createThirdPartyUser(@Body() userReq: CreateUserDto) {
    const response = await this.userService.create(userReq, true);

    return response;
  }

  @Post('notify-all-users')
  @ApiSecurity('bearer')
  // @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async notifyAllUsers(@Req() req: IRequest, @Body() info: { title: string; message: string; path: string }) {
    const res = await this.userService.notifyAllUsers(info);

    return {
      message: 'Push notification subscription sent',
      data: res,
    };
  }

  @Post('notify-users')
  @ApiSecurity('bearer')
  @ApiOperation({
    summary: 'Send notifications to specific users',
    description:
      'Send push notifications to users by email (single or multiple), by country, or to all users. Use user_email for single user, emails array for multiple users, country for country-specific, or leave all empty for all users.',
  })
  @ApiBody({
    type: NotificationInfoDto,
    description: 'Notification details with targeting options',
  })
  // @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async notifyUsers(@Req() req: IRequest, @Body() info: NotificationInfoDto) {
    const res = await this.userService.notifyUsers(info);

    return {
      message: 'Push notification sent successfully',
      data: res,
    };
  }

  @Get('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getUsers(@Query('pagination') pagination: PaginatedQueryDto, @Query('filter') filter: FilterUserDto) {
    const data = await this.userService.getUsers(pagination, filter);
    return {
      message: 'Users fetched successfully',
      ...data,
    };
  }

  @Get('csv/phone/:country')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getPhoneExport(@Param('country') country: string, @Res() res: IResponse) {
    const buffer = await this.userService.getPhoneCSV(country);
    const readable = new Readable();
    readable._read = () => {};
    readable.push(buffer);
    readable.push(null);
    readable.pipe(res);
    return {};
  }

  @Get('/get-debtors')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async getDebtors() {
    const data = await this.userService.getDebtors();
    return {
      message: 'Debtors fetched successfully',
      data,
    };
  }

  @Get('/update-debtor-deadlines')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateDebtorDealines() {
    const data = await this.userService.updateDebtorSubscriptionsDeadline();
    return {
      message: 'Debtor deadlines updated successfully',
      data,
    };
  }

  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiExcludeEndpoint()
  @ApiHeader({ name: 'x-internal-api-key' })
  async messageDebtors() {
    const data = await this.userService.messageDebtors();
    return {
      message: 'Debtors fetched successfully',
      data,
    };
  }

  @Post('/migrate-user-phonenumbers')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateUserPhoneNumbers() {
    const data = await this.userService.migrateUserPhoneNumbers();
    return {
      message: 'User phones migrated successfully',
      data,
    };
  }

  @Post('/update-mailchimp-customers')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateMailchimpCustomers() {
    const data = await this.userService.updateMailchimpList();
    return {
      message: 'Mailchimp users updated successfully',
      data,
    };
  }

  @Post('/add-users-to-customer-io')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async addUsersT() {
    const data = await this.userService.createUsersOnCustomerIo();
    return data;
  }

  @Post('/migrate-primary-store')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migratePrimaryStore() {
    const response = await this.userService.migratePrimaryStore();
    return response;
  }

  @Post('/migrate-push-notification-country')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migratePushNotificationCountry() {
    const response = await this.userService.migratePushNotificationCountry();
    return response;
  }

  @Post('/:id/update-subscription')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateSubscriptionStatus(@Param('id') id: Types.ObjectId, @Body() body: UpdateUserSubscriptionDto) {
    const data = await this.userService.updateUserSubscription(id, body);

    return {
      message: 'User subscription updated successfully',
      data,
    };
  }

  @Get('/admin/:id')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async getUser(@Param('id') id: string) {
    const data = await this.userService.adminGetUser(id);

    return {
      message: 'User fetched successfully',
      data,
    };
  }

  @Put('/:id/mark-as-qualified')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async markUserAsQualified(@Param('id') id: string) {
    const data = await this.userService.markUserAsQualified(id);

    return {
      message: 'User marked as qualified successfully',
      data,
    };
  }

  @Post('/migrate-users-sub')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateUsersSub(@Req() req: IRequest) {
    const data = await this.userService.migrateUsersSub();
    return {
      message: 'Subscriptions migrated',
      data,
    };
  }

  @Put('/change-country')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Change the country for a user and all related data' })
  @ApiResponse({ status: 200, description: 'Country updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async changeCountry(@Req() req: IRequest, @Body() body: ChangeCountryDto) {
    const user = req.user as User;

    if (!body.country || !Object.values(COUNTRY_CODE).includes(body.country)) {
      throw new BadRequestException('Invalid country code');
    }

    return await this.userService.changeUserCountry(user.id, body.country);
  }

  @Put('/admin/change-country')
  @ApiSecurity('bearer')
  // @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Admin endpoint to change the country for any user' })
  @ApiResponse({ status: 200, description: 'Country updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiBody({
    type: AdminChangeCountryDto,
    description: 'Change country using user email',
    schema: {
      example: {
        email: '<EMAIL>',
        country: 'NG',
      },
    },
  })
  async adminChangeCountry(@Body() body: AdminChangeCountryDto) {
    if (!body.country || !Object.values(COUNTRY_CODE).includes(body.country)) {
      throw new BadRequestException('Invalid country code');
    }

    const user = await this.userService.getUserByEmail(body.email);
    return await this.userService.changeUserCountry(user.id, body.country);
  }

  // @Post('/test-messaging')
  // @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  // async testMessaging(@Req() req: IRequest) {
  //   const data = await this.messagingRepository.sendSignupCheckInMessage('2348052291107', { customerName: 'Silas' });
  //   return {
  //     message: 'Message Sent',
  //     data,
  //   };
  // }

  @Get('internal/dashboard-users')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiHeader({ name: 'Authorization', description: 'Bearer token' })
  @ApiOperation({ summary: 'Get all users with internal dashboard roles (requires MANAGE_DASHBOARD_USERS permission)' })
  @InternalDashboardPermissions(SCOPES.INTERNAL_DASHBOARD.MANAGE_DASHBOARD_USERS)
  async getInternalDashboardUsers() {
    return await this.userService.getInternalDashboardUsers();
  }

  @Post('internal/dashboard-roles/:userId')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiHeader({ name: 'Authorization', description: 'Bearer token' })
  @ApiOperation({ summary: 'Assign an internal dashboard role to a user (requires MANAGE_DASHBOARD_USERS permission)' })
  @InternalDashboardPermissions(SCOPES.INTERNAL_DASHBOARD.MANAGE_DASHBOARD_USERS)
  async assignInternalDashboardRole(@Param('userId') userId: string, @Body() body: InternalDashboardRoleDto) {
    return await this.userService.assignInternalDashboardRole(userId, body.role);
  }

  @Delete('internal/dashboard-roles/:userId')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiHeader({ name: 'Authorization', description: 'Bearer token' })
  @ApiOperation({ summary: 'Remove internal dashboard role from a user (requires MANAGE_DASHBOARD_USERS permission)' })
  @InternalDashboardPermissions(SCOPES.INTERNAL_DASHBOARD.MANAGE_DASHBOARD_USERS)
  async removeInternalDashboardRole(@Param('userId') userId: string) {
    return await this.userService.removeInternalDashboardRole(userId);
  }

  @Get('internal/roles')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiHeader({ name: 'Authorization', description: 'Bearer token' })
  @ApiOperation({ summary: 'Get all available internal dashboard roles' })
  async getInternalRoles() {
    return Object.values(INTERNAL_ROLES);
  }

  @Get('internal/admin-test')
  // Guards were commented out for testing, now restore them
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiHeader({ name: 'Authorization', description: 'Bearer token' })
  @InternalDashboardPermissions(SCOPES.INTERNAL_DASHBOARD.MANAGE_DASHBOARD_USERS)
  async internalAdminTest() {
    return { message: 'You have admin access with MANAGE_DASHBOARD_USERS permission' };
  }
}
