import { Controller } from '@nestjs/common';
import { AdminConfigService } from './adminconfig.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class AdminConfigBroker {
  constructor(private adminConfigService: AdminConfigService) {}

  @MessagePattern(BROKER_PATTERNS.CONFIG.GET_CONFIG)
  async getActiveConfig(name: string) {
    return this.adminConfigService.getConfig(name);
  }

  @MessagePattern(BROKER_PATTERNS.CONFIG.GET_APP_CONFIGURATION)
  async getAppConfiguration() {
    return this.adminConfigService.getAppConfiguration();
  }

  @MessagePattern(BROKER_PATTERNS.CONFIG.UPDATE_MOBILE_APP_SIGNIN_COUNTER)
  async updateMobileAppSigninCounter(counter: number) {
    return this.adminConfigService.updateMobileAppSigninCounter(counter);
  }
}
