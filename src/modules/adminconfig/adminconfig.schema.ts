import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import { WITHDRAWAL_PROVIDERS } from '../wallets/wallet.withdrawal.schema';

export enum ADMIN_CONFIG {
  WITHDRAWAL_PROVIDER = 'WITHDRAWAL_PROVIDER',
  TRANSFERS_PROVIDER = 'TRANSFERS_PROVIDER',
  BVN_PROVIDER = 'BVN_PROVIDER',
  FEATURE_FLAGS = 'FEATURE_FLAGS',
  MINIMUM_APP_VERSION = 'MINIMUM_APP_VERSION',
  MOBILE_APP_SIGNIN_COUNTER = 'MOBILE_APP_SIGNIN_COUNTER',
}

export interface FeatureFlags {
  subscriptions: boolean;
  deliveries: boolean;
  welcome_back_promo: boolean;
  store_reviews: boolean;
}

export type AdminConfigDocument = Document & AdminConfig;

@Schema()
export class AdminConfig {
  _id: any;

  @ApiProperty()
  @Prop({ type: String, enum: Object.values(ADMIN_CONFIG), required: true, unique: true })
  key: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  value: string;
}

export const AdminConfigSchema = SchemaFactory.createForClass<AdminConfig>(AdminConfig);
